@using Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorList
@using Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleList
@using Klee.Web.App.Components.Pages.VoyagePlanning
@using Klee.Web.App.Components.Pages.InvoiceManagement.Invoices
@using Klee.Web.App.Components.Pages.VoyageManagement.VoyageList
@using Klee.Web.App.Components.Pages.VoyageManagement.FindVoyage
@inherits LayoutComponentBase

<AntContainer/>
<div class="flex flex-col bg-gray-50 h-full">
    <header class="bg-white border-b border-gray-200">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <a href="@Dashboard.GetUri()" class="flex items-center space-x-2">
                        <img src="/Logo/logo.png" alt="SEAFAR Logo" class="h-8 w-16" />
                        <span class="text-xl font-bold text-teal-700">SEAFAR</span>
                    </a>
                </div>

                <div class="flex items-center space-x-8">
                    <nav class="hidden md:flex items-center space-x-6">
                        <a href="@VoyagePlanner.GetUri()" class="text-gray-600 hover:text-teal-700 font-medium">Find a Captain</a>
                        <a href="@FindVoyage.GetUri()" class="text-gray-600 hover:text-teal-700 font-medium">Find a Voyage</a>
                        <a href="@Invoices.GetUri()" class="text-gray-600 hover:text-teal-700 font-medium">Invoices</a>
                        <a href="@Voyages.GetUri()" class="text-gray-600 hover:text-teal-700 font-medium">My Voyages</a>
                        <button class="dropdown-toggle text-gray-600 hover:text-teal-700 font-medium" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            My Organization
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="@(Vessels.GetUri())">
                                    <i class="fas fa-ship mr-2"></i>
                                    Vessels
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="@(Operators.GetUri())">
                                    <i class="fas fa-users mr-2"></i>
                                    Operators
                                </a>
                            </li>
@*                             <li>
                                <a class="dropdown-item" href="@(Rocs.GetUri())">
                                    <i class="fas fa-building ml-1 mr-2"></i>
                                    ROCs
                                </a>
                            </li> *@
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </header>

    <main class="flex-1 overflow-auto bg-white">
        <div class="container mx-auto px-4 py-6">
            @Body
        </div>
    </main>

</div>


@code {
}