@page "/dashboard"
@using Klee.Web.App.Components.Pages.VoyagePlanning
@layout OrganizationViewLayout

<div class="space-y-6">
    <!-- Main Action Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
        <ActionCard
            Href="@VoyagePlanner.GetUri()"
            Title="Find a Captain"
            Description="Find a Remote Operator for your vessel"
            IconClass="fas fa-user"
            ButtonText="Start" />

        <ActionCard
            Href="/find-voyage"
            Title="Find a Voyage"
            Description="Browse open voyages looking for Remote Operators"
            IconClass="fas fa-ship"
            ButtonText="Start" />
    </div>

</div>

@code {

    public static string GetUri() {
        return "/dashboard";
    }

}